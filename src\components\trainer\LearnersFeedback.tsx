import React from 'react';
import { Star, TrendingUp, Award, Users } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

export interface TopProgram {
  name: string;
  rating: number;
  participants: number;
  feedback: string;
  category?: string;
}

export interface LearnersFeedbackProps {
  npsScore: number;
  topPrograms: TopProgram[];
}

const LearnersFeedback: React.FC<LearnersFeedbackProps> = ({ npsScore, topPrograms }) => {
  const getNPSColor = (score: number) => {
    if (score >= 70) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 50) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getNPSLabel = (score: number) => {
    if (score >= 70) return 'Excellent';
    if (score >= 50) return 'Good';
    if (score >= 30) return 'Acceptable';
    return 'Needs Improvement';
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating) 
            ? 'fill-yellow-400 text-yellow-400' 
            : index < rating 
            ? 'fill-yellow-200 text-yellow-400' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="space-y-6">
      {/* NPS Score Section */}
      <div className="text-center">
        <div className="mb-4">
          <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full border ${getNPSColor(npsScore)}`}>
            <TrendingUp className="w-5 h-5" />
            <span className="font-semibold">Net Promoter Score</span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="text-4xl font-bold text-primary">{npsScore}</div>
          <div className="text-sm text-muted-foreground">{getNPSLabel(npsScore)}</div>
          <Progress value={npsScore} className="h-2 max-w-xs mx-auto" />
        </div>
      </div>

      {/* Top Programs Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <Award className="w-5 h-5 text-primary" />
          <h3 className="font-semibold text-sm">Top Rated Programs</h3>
        </div>

        {topPrograms.slice(0, 3).map((program, index) => (
          <div key={program.name} className="p-4 rounded-lg border bg-card hover:shadow-sm transition-shadow">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <Badge variant="outline" className="text-xs bg-primary/10 text-primary border-primary/20">
                    #{index + 1}
                  </Badge>
                  <span className="font-medium text-sm">{program.name}</span>
                  {program.category && (
                    <Badge variant="secondary" className="text-xs">
                      {program.category}
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-3 mb-2">
                  <div className="flex items-center gap-1">
                    {renderStars(program.rating)}
                    <span className="text-sm font-medium ml-1">{program.rating.toFixed(1)}</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Users className="w-3 h-3" />
                    {program.participants} participants
                  </div>
                </div>
                
                <p className="text-xs text-muted-foreground italic">
                  "{program.feedback}"
                </p>
              </div>
            </div>
            
            <div className="flex justify-between items-center text-xs">
              <span className="text-muted-foreground">Rating</span>
              <div className="flex items-center gap-1">
                <div className="w-16 bg-gray-200 rounded-full h-1.5">
                  <div 
                    className="bg-yellow-400 h-1.5 rounded-full" 
                    style={{ width: `${(program.rating / 5) * 100}%` }}
                  ></div>
                </div>
                <span className="font-medium">{Math.round((program.rating / 5) * 100)}%</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 gap-4 pt-4 border-t">
        <div className="text-center">
          <div className="text-lg font-bold text-primary">
            {topPrograms.reduce((sum, p) => sum + p.participants, 0)}
          </div>
          <div className="text-xs text-muted-foreground">Total Participants</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-primary">
            {(topPrograms.reduce((sum, p) => sum + p.rating, 0) / topPrograms.length).toFixed(1)}
          </div>
          <div className="text-xs text-muted-foreground">Avg Rating</div>
        </div>
      </div>
    </div>
  );
};

export default LearnersFeedback;
