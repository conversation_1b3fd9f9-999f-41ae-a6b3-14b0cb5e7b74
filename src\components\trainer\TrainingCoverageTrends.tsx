import React, { useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Calendar, Users } from 'lucide-react';

export interface MonthlyTrainingData {
  month: string;
  employees: number;
  target?: number;
}

export interface TrainingProgram {
  id: string;
  name: string;
  monthlyData: MonthlyTrainingData[];
  color?: string;
  category?: string;
}

export interface TrainingCoverageTrendsProps {
  programs: TrainingProgram[];
  defaultProgramId?: string;
  timeframe?: 'monthly' | 'quarterly' | 'ytd';
}

const TrainingCoverageTrends: React.FC<TrainingCoverageTrendsProps> = ({ 
  programs, 
  defaultProgramId,
  timeframe = 'monthly'
}) => {
  const [selectedProgramId, setSelectedProgramId] = useState(
    defaultProgramId || programs[0]?.id || ''
  );
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);

  const selectedProgram = programs.find(p => p.id === selectedProgramId);

  const getChartOptions = (): Highcharts.Options => {
    if (!selectedProgram) return {};

    const categories = selectedProgram.monthlyData.map(d => d.month);
    const employeesData = selectedProgram.monthlyData.map(d => d.employees);
    const targetData = selectedProgram.monthlyData.map(d => d.target || 0);

    return {
      chart: {
        type: 'column',
        height: 350,
        backgroundColor: 'transparent',
        style: {
          fontFamily: 'Inter, system-ui, sans-serif'
        }
      },
      title: { text: null },
      xAxis: {
        categories,
        title: {
          text: 'Month'
        },
        labels: {
          style: {
            fontSize: '11px'
          }
        }
      },
      yAxis: {
        title: {
          text: 'Number of Employees'
        },
        min: 0,
        labels: {
          style: {
            fontSize: '11px'
          }
        }
      },
      series: [
        {
          type: 'column',
          name: 'Employees Trained',
          data: employeesData,
          color: selectedProgram.color || '#3b82f6',
          borderRadius: 4,
          animation: {
            duration: 800
          }
        },
        {
          type: 'line',
          name: 'Target',
          data: targetData,
          color: '#ef4444',
          marker: {
            enabled: true,
            radius: 4
          },
          lineWidth: 2,
          dashStyle: 'ShortDash'
        }
      ],
      legend: {
        enabled: true,
        align: 'center',
        verticalAlign: 'bottom'
      },
      credits: { enabled: false },
      tooltip: {
        shared: true,
        formatter: function() {
          let tooltip = `<b>${this.x}</b><br/>`;
          this.points?.forEach(point => {
            tooltip += `<span style="color:${point.color}">\u25CF</span> ${point.series.name}: <b>${point.y}</b><br/>`;
          });
          return tooltip;
        }
      },
      plotOptions: {
        column: {
          borderWidth: 0,
          pointPadding: 0.1,
          groupPadding: 0.1
        },
        series: {
          animation: {
            duration: 800
          }
        }
      }
    };
  };

  const getTotalTrained = () => {
    if (!selectedProgram) return 0;
    return selectedProgram.monthlyData.reduce((sum, data) => sum + data.employees, 0);
  };

  const getAveragePerMonth = () => {
    if (!selectedProgram || selectedProgram.monthlyData.length === 0) return 0;
    return Math.round(getTotalTrained() / selectedProgram.monthlyData.length);
  };

  const getTargetAchievement = () => {
    if (!selectedProgram) return 0;
    const totalTarget = selectedProgram.monthlyData.reduce((sum, data) => sum + (data.target || 0), 0);
    const totalTrained = getTotalTrained();
    return totalTarget > 0 ? Math.round((totalTrained / totalTarget) * 100) : 0;
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-primary" />
          <h3 className="font-semibold text-sm">Training Coverage Trends</h3>
        </div>
        
        <div className="flex gap-2">
          <Select value={selectedProgramId} onValueChange={setSelectedProgramId}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select Program" />
            </SelectTrigger>
            <SelectContent>
              {programs.map((program) => (
                <SelectItem key={program.id} value={program.id}>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: program.color || '#3b82f6' }}
                    ></div>
                    {program.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={selectedTimeframe} onValueChange={(value: any) => setSelectedTimeframe(value)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="ytd">YTD</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Selected Program Info */}
      {selectedProgram && (
        <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
          <div 
            className="w-4 h-4 rounded-full" 
            style={{ backgroundColor: selectedProgram.color || '#3b82f6' }}
          ></div>
          <span className="font-medium text-sm">{selectedProgram.name}</span>
          {selectedProgram.category && (
            <Badge variant="secondary" className="text-xs">
              {selectedProgram.category}
            </Badge>
          )}
        </div>
      )}

      {/* Chart */}
      <div className="h-[350px]">
        <HighchartsReact highcharts={Highcharts} options={getChartOptions()} />
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t">
        <div className="text-center p-3 bg-primary/5 rounded-lg">
          <div className="text-2xl font-bold text-primary">{getTotalTrained()}</div>
          <div className="text-xs text-muted-foreground">Total Trained ({selectedTimeframe})</div>
        </div>
        <div className="text-center p-3 bg-secondary/50 rounded-lg">
          <div className="text-2xl font-bold text-secondary-foreground">{getAveragePerMonth()}</div>
          <div className="text-xs text-muted-foreground">Avg per Month</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{getTargetAchievement()}%</div>
          <div className="text-xs text-muted-foreground">Target Achievement</div>
        </div>
      </div>

      {/* Detailed Analysis */}
      {selectedProgram && (
        <div className="space-y-4 mt-6">
          {/* Monthly Performance Breakdown */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-muted-foreground flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Monthly Performance Analysis
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Best and Worst Performing Months */}
              <div className="space-y-3">
                <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-green-800">Best Month</span>
                    <TrendingUp className="w-4 h-4 text-green-600" />
                  </div>
                  {(() => {
                    const bestMonth = selectedProgram.monthlyData.reduce((prev, current) =>
                      (current.employees > prev.employees) ? current : prev
                    );
                    return (
                      <div>
                        <div className="text-lg font-bold text-green-700">{bestMonth.month}</div>
                        <div className="text-sm text-green-600">{bestMonth.employees} employees trained</div>
                        <div className="text-xs text-green-500">
                          {bestMonth.target ? `${Math.round((bestMonth.employees / bestMonth.target) * 100)}% of target` : 'No target set'}
                        </div>
                      </div>
                    );
                  })()}
                </div>

                <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-orange-800">Needs Attention</span>
                    <Users className="w-4 h-4 text-orange-600" />
                  </div>
                  {(() => {
                    const worstMonth = selectedProgram.monthlyData.reduce((prev, current) =>
                      (current.employees < prev.employees) ? current : prev
                    );
                    return (
                      <div>
                        <div className="text-lg font-bold text-orange-700">{worstMonth.month}</div>
                        <div className="text-sm text-orange-600">{worstMonth.employees} employees trained</div>
                        <div className="text-xs text-orange-500">
                          {worstMonth.target ? `${Math.round((worstMonth.employees / worstMonth.target) * 100)}% of target` : 'No target set'}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>

              {/* Trend Analysis */}
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-800">Growth Trend</span>
                    <TrendingUp className="w-4 h-4 text-blue-600" />
                  </div>
                  {(() => {
                    const firstHalf = selectedProgram.monthlyData.slice(0, 6).reduce((sum, month) => sum + month.employees, 0);
                    const secondHalf = selectedProgram.monthlyData.slice(6).reduce((sum, month) => sum + month.employees, 0);
                    const growth = ((secondHalf - firstHalf) / firstHalf * 100);
                    return (
                      <div>
                        <div className={`text-lg font-bold ${growth >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                          {growth >= 0 ? '+' : ''}{growth.toFixed(1)}%
                        </div>
                        <div className="text-sm text-blue-600">H2 vs H1 growth</div>
                        <div className="text-xs text-blue-500">
                          {growth >= 0 ? 'Positive trend' : 'Declining trend'}
                        </div>
                      </div>
                    );
                  })()}
                </div>

                <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-purple-800">Consistency</span>
                    <Badge className="w-4 h-4 text-purple-600" />
                  </div>
                  {(() => {
                    const values = selectedProgram.monthlyData.map(m => m.employees);
                    const mean = values.reduce((a, b) => a + b, 0) / values.length;
                    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
                    const stdDev = Math.sqrt(variance);
                    const consistency = Math.max(0, 100 - (stdDev / mean * 100));
                    return (
                      <div>
                        <div className="text-lg font-bold text-purple-700">{consistency.toFixed(0)}%</div>
                        <div className="text-sm text-purple-600">Consistency Score</div>
                        <div className="text-xs text-purple-500">
                          {consistency >= 80 ? 'Very consistent' : consistency >= 60 ? 'Moderately consistent' : 'Highly variable'}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>

          {/* Monthly Details Table */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-muted-foreground">Monthly Breakdown</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {selectedProgram.monthlyData.map((month, index) => {
                const achievement = month.target ? (month.employees / month.target) * 100 : 0;
                const isAboveTarget = achievement >= 100;
                return (
                  <div key={index} className={`p-2 rounded border ${
                    isAboveTarget ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="text-sm font-medium">{month.month}</div>
                    <div className="text-xs text-muted-foreground">
                      {month.employees} / {month.target || 'No target'}
                    </div>
                    {month.target && (
                      <div className={`text-xs font-medium ${
                        isAboveTarget ? 'text-green-600' : 'text-orange-600'
                      }`}>
                        {achievement.toFixed(0)}%
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Insights and Recommendations */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-muted-foreground">Key Insights</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="text-sm font-medium text-blue-800 mb-2">Performance Insights</div>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• {selectedProgram.category} training shows {getTargetAchievement() >= 90 ? 'excellent' : getTargetAchievement() >= 70 ? 'good' : 'needs improvement'} target achievement</li>
                  <li>• Average monthly training: {getAveragePerMonth()} employees</li>
                  <li>• Total program reach: {getTotalTrained()} employees this year</li>
                </ul>
              </div>

              <div className="p-3 bg-amber-50 rounded-lg">
                <div className="text-sm font-medium text-amber-800 mb-2">Recommendations</div>
                <ul className="text-xs text-amber-700 space-y-1">
                  {getTargetAchievement() < 80 && <li>• Consider increasing training frequency</li>}
                  {getAveragePerMonth() < 50 && <li>• Explore additional training channels</li>}
                  <li>• Monitor seasonal patterns for better planning</li>
                  <li>• Set realistic monthly targets based on trends</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrainingCoverageTrends;
