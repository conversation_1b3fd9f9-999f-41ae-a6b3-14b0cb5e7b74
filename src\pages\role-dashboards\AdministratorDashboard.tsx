import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from '@/contexts/AuthContext';
import { Progress } from "@/components/ui/progress";
import {
  Users,
  UserPlus,
  Clock,
  Building2,
  Award,
  MessageSquare,
  BarChart3,
  BookOpen,
  TrendingUp,
  Target,
  Heart,
  Activity,
  Star,
  CheckCircle,
  AlertCircle,
  Calendar,
  Settings,
  Shield,
  Database,
  Monitor,
  FileText,
  Zap,
  Globe,
  UserCheck,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { MetricCard } from "@/components/Metric Charts";
import MetricDetails from "@/components/Metric Charts/MetricDetails";

export const AdministratorDashboard: React.FC = () => {
  const { user } = useAuth();

  // State management for metric cards
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});

  // Use the same profile data structure as AdministratorProfile component
  const administrator = {
    name: user?.name || "Alex Kumar",
    role: "System Administrator",
    avatar: user?.avatar || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    department: user?.department || "IT Department"
  };

  // Enhanced dashboard data with additional metrics
  const dashboardData = {
    totalActiveLearners: { current: 2800, total: 3000 },
    totalOnboarded: 200,
    // Monthly onboarding data for YTD
    monthlyOnboardingData: [
      { month: 'Jan', count: 25, target: 20 },
      { month: 'Feb', count: 18, target: 20 },
      { month: 'Mar', count: 22, target: 20 },
      { month: 'Apr', count: 28, target: 20 },
      { month: 'May', count: 24, target: 20 },
      { month: 'Jun', count: 26, target: 20 },
      { month: 'Jul', count: 15, target: 20 }, // Current month (partial)
    ],
    // Training Metrics for Top 4 Boxes
    trainingMetrics: [
      {
        id: "mandatory-training",
        title: "12-Month Rolling Completion & Application Rate for Mandatory Training",
        value: "87.3",
        unit: "%",
        target: 90,
        targetPercentage: 40,
        trend: [78, 79, 80, 81, 82, 82.5, 83, 83.5, 84, 84.5, 85, 85.2, 85.5, 85.8, 86, 86.2, 86.5, 86.7, 87, 87.1, 87.1, 87.2, 87.2, 87.3],
        isImproving: true,
        mtdVariationFromPY: 2.7,
        mtdCurrentValue: 87.3,
        mtdPreviousYearValue: 85.0,
        helpText: "The percentage of mandatory training modules completed and, where applicable, applied in the work environment—validated through assessments, supervisor feedback, or task performance"
      },
      {
        id: "service-excellence",
        title: "12-Month Rolling Service Excellence Score",
        value: "4.2",
        unit: "/5.0",
        target: 4.5,
        targetPercentage: -15.4,
        trend: [3.5, 3.6, 3.7, 3.8, 3.8, 3.9, 3.9, 4.0, 4.0, 4.0, 4.1, 4.1, 4.1, 4.1, 4.1, 4.2, 4.2, 4.2, 4.2, 4.2, 4.2, 4.2, 4.2, 4.2],
        isImproving: false,
        mtdVariationFromPY: -6.7,
        mtdCurrentValue: 4.2,
        mtdPreviousYearValue: 4.5,
        helpText: "A measure of service quality based on customer feedback, internal audits, or predefined standards"
      },
      {
        id: "competence-index",
        title: "12-month Rolling Competence Index for Critical Training Programs",
        value: "78.5",
        unit: "%",
        target: 85,
        targetPercentage: 15.4,
        trend: [68, 69, 70, 71, 72, 72.5, 73, 73.5, 74, 74.5, 75, 75.5, 76, 76.2, 76.5, 77, 77.2, 77.5, 77.8, 78, 78.1, 78.2, 78.3, 78.5],
        isImproving: true,
        mtdVariationFromPY: 7.6,
        mtdCurrentValue: 78.5,
        mtdPreviousYearValue: 73.0,
        helpText: "A measure of how reliably individuals understand and apply critical knowledge in high-risk training areas, where overconfidence or incorrect assumptions can lead to serious consequences. This index is generated through an algorithm that evaluates both the accuracy of responses and the confidence behind them, providing a more accurate reflection of true competence."
      },
      {
        id: "knowledge-reactivation",
        title: "12-month Rolling Knowledge Reactivation Index",
        value: "92.1",
        unit: "%",
        target: 95,
        targetPercentage: 22.9,
        trend: [85, 86, 87, 88, 88.2, 88.5, 89, 89.2, 89.5, 90, 90.2, 90.5, 90.8, 91, 91.1, 91.2, 91.3, 91.5, 91.6, 91.8, 91.9, 92, 92.1, 92.1],
        isImproving: true,
        mtdVariationFromPY: 3.1,
        mtdCurrentValue: 92.1,
        mtdPreviousYearValue: 89.3,
        helpText: "Reflects how well individuals retain and reinforce critical knowledge over time. Calculated from participation and scores in post-training and follow-up quizzes delivered via push notifications using spaced repetition techniques. A higher index indicates stronger long-term retention and engagement."
      }
    ],
    groupLearningCoverage: [
      { name: "HROB", status: "active", completion: 95 },
      { name: "Medcare Look", status: "active", completion: 88 },
      { name: "GMO", status: "active", completion: 92 },
      { name: "NFS", status: "active", completion: 85 },
      { name: "Aster Care", status: "active", completion: 90 }
    ],
    trainingHours: {
      selfPaced: 45,
      instructorLed: 30,
      virtual: 25,
      onTheJob: 35,
      hybrid: 20
    },
    activeCertifications: {
      bls: 85,
      acls: 78,
      pals: 92,
      heartSaver: 88
    },
    learnersFeedback: {
      nps: 94,
      topPrograms: ["Emergency Response", "Patient Safety", "Clinical Excellence"]
    },
    programCategories: {
      managerial: 15,
      behavioural: 22,
      functional: 18,
      technical: 25,
      clinical: 30
    },
    overallTrainingPrograms: {
      selfPaced: 120,
      instructorLed: 85,
      virtual: 95,
      onTheJob: 110,
      hybrid: 75
    },
    // Additional admin metrics
    systemHealth: {
      uptime: 99.8,
      activeUsers: 2847,
      systemLoad: 65,
      storage: 78
    },
    recentActivity: [
      { action: "New user registration", count: 15, time: "2 hours ago" },
      { action: "Course completions", count: 42, time: "4 hours ago" },
      { action: "System backup", count: 1, time: "6 hours ago" }
    ]
  };

  // Enhanced chart configurations with modern styling
  const trainingHoursPieOptions = {
    chart: {
      type: 'pie',
      height: 220,
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Inter, system-ui, sans-serif'
      }
    },
    title: { text: null },
    series: [{
      name: 'Hours',
      data: [
        { name: 'Self Paced', y: dashboardData.trainingHours.selfPaced, color: '#64003e' },
        { name: 'Instructor Led', y: dashboardData.trainingHours.instructorLed, color: '#8b1538' },
        { name: 'Virtual', y: dashboardData.trainingHours.virtual, color: '#b12a47' },
        { name: 'On the Job', y: dashboardData.trainingHours.onTheJob, color: '#d63f56' },
        { name: 'Hybrid', y: dashboardData.trainingHours.hybrid, color: '#f55465' }
      ],
      innerSize: '60%',
      dataLabels: {
        enabled: true,
        distance: 15,
        format: '{point.name}<br/>{point.y}h',
        style: {
          fontWeight: 'bold',
          color: '#333333',
          textOutline: '1px contrast',
          fontSize: '11px'
        },
        connectorColor: '#666666',
        connectorWidth: 1
      }
    }],
    legend: { enabled: false },
    credits: { enabled: false },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        showInLegend: false,
        borderWidth: 2,
        borderColor: '#ffffff'
      }
    },
    tooltip: {
      pointFormat: '<b>{point.percentage:.1f}%</b><br/>Hours: {point.y}'
    }
  };

  // Enhanced pie chart for Active Life Support Certifications
  const certificationsPieOptions = {
    chart: {
      type: 'pie',
      height: 220,
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Inter, system-ui, sans-serif'
      }
    },
    title: { text: null },
    series: [{
      name: 'Percentage',
      data: [
        { name: 'BLS', y: dashboardData.activeCertifications.bls, color: '#64003e' },
        { name: 'ACLS', y: dashboardData.activeCertifications.acls, color: '#8b1538' },
        { name: 'PALS', y: dashboardData.activeCertifications.pals, color: '#b12a47' },
        { name: 'Heart Saver', y: dashboardData.activeCertifications.heartSaver, color: '#d63f56' }
      ],
      dataLabels: {
        enabled: true,
        distance: 15,
        format: '{point.name}<br/>{point.y}%',
        style: {
          fontWeight: 'bold',
          color: '#333333',
          textOutline: '1px contrast',
          fontSize: '11px'
        },
        connectorColor: '#666666',
        connectorWidth: 1
      }
    }],
    legend: { enabled: false },
    credits: { enabled: false },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        showInLegend: false,
        borderWidth: 2,
        borderColor: '#ffffff'
      }
    },
    tooltip: {
      pointFormat: '<b>{point.percentage:.1f}%</b><br/>Certified: {point.y}%'
    }
  };

  // Enhanced bar chart for Program Categories
  const programCategoriesChartOptions = {
    chart: {
      type: 'bar',
      height: 220,
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Inter, system-ui, sans-serif'
      }
    },
    title: { text: null },
    xAxis: {
      categories: ['Clinical', 'Technical', 'Behavioural', 'Functional', 'Managerial'],
      title: { text: null },
      labels: {
        style: {
          fontSize: '12px',
          color: '#64003e',
          fontWeight: '500'
        }
      },
      lineWidth: 0,
      tickWidth: 0
    },
    yAxis: {
      title: { text: null },
      labels: { enabled: false },
      gridLineWidth: 0,
      lineWidth: 0
    },
    series: [{
      name: 'Programs',
      data: [
        { y: dashboardData.programCategories.clinical, color: '#64003e' },
        { y: dashboardData.programCategories.technical, color: '#8b1538' },
        { y: dashboardData.programCategories.behavioural, color: '#b12a47' },
        { y: dashboardData.programCategories.functional, color: '#d63f56' },
        { y: dashboardData.programCategories.managerial, color: '#f55465' }
      ],
      dataLabels: {
        enabled: true,
        format: '{point.y}',
        style: {
          color: 'white',
          fontWeight: 'bold',
          textOutline: 'none'
        }
      }
    }],
    legend: { enabled: false },
    credits: { enabled: false },
    plotOptions: {
      bar: {
        borderWidth: 0,
        groupPadding: 0.1,
        pointPadding: 0.05,
        borderRadius: 4
      }
    },
    tooltip: {
      pointFormat: '<b>{point.y}</b> programs'
    }
  };

  // Monthly Onboarding Trend Chart
  const onboardingTrendOptions = {
    chart: {
      type: 'column',
      height: 180,
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Inter, system-ui, sans-serif'
      }
    },
    title: { text: null },
    xAxis: {
      categories: dashboardData.monthlyOnboardingData.map(item => item.month),
      title: { text: null },
      labels: {
        style: {
          fontSize: '11px',
          color: '#666666',
          fontWeight: '500'
        }
      },
      lineWidth: 1,
      lineColor: '#e5e7eb',
      tickWidth: 0
    },
    yAxis: {
      title: {
        text: 'Employees',
        style: {
          fontSize: '11px',
          color: '#666666',
          fontWeight: '500'
        }
      },
      labels: {
        style: {
          fontSize: '11px',
          color: '#666666'
        }
      },
      gridLineWidth: 1,
      gridLineColor: '#f3f4f6',
      min: 0
    },
    series: [
      {
        name: 'Onboarded',
        data: dashboardData.monthlyOnboardingData.map(item => item.count),
        color: '#3b82f6',
        borderRadius: 3,
        dataLabels: {
          enabled: true,
          style: {
            fontSize: '10px',
            color: '#ffffff',
            fontWeight: 'bold',
            textOutline: 'none'
          }
        }
      },
      {
        name: 'Target',
        data: dashboardData.monthlyOnboardingData.map(item => item.target),
        color: '#94a3b8',
        borderRadius: 3,
        dataLabels: {
          enabled: true,
          style: {
            fontSize: '10px',
            color: '#ffffff',
            fontWeight: 'bold',
            textOutline: 'none'
          }
        }
      }
    ],
    legend: {
      enabled: true,
      align: 'center',
      verticalAlign: 'bottom',
      itemStyle: {
        fontSize: '11px',
        color: '#666666',
        fontWeight: '500'
      }
    },
    credits: { enabled: false },
    plotOptions: {
      column: {
        animation: {
          duration: 1000
        },
        borderWidth: 0,
        groupPadding: 0.1,
        pointPadding: 0.05
      }
    },
    tooltip: {
      shared: true,
      backgroundColor: '#ffffff',
      borderColor: '#e5e7eb',
      borderRadius: 8,
      shadow: true,
      style: {
        fontSize: '12px'
      },
      formatter: function() {
        let tooltip = `<b>${this.x}</b><br/>`;
        this.points.forEach((point: any) => {
          tooltip += `<span style="color:${point.color}">●</span> ${point.series.name}: <b>${point.y}</b><br/>`;
        });
        return tooltip;
      }
    }
  };

  // Speedometer chart for Active Learners
  const progressPercentage = (dashboardData.totalActiveLearners.current / dashboardData.totalActiveLearners.total) * 100;
  const speedometerOptions = {
    chart: {
      type: 'pie',
      height: 250,
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Inter, system-ui, sans-serif'
      }
    },
    title: { text: null },
    series: [{
      name: 'Progress',
      data: [
        {
          name: 'Active',
          y: progressPercentage,
          color: {
            linearGradient: { x1: 0, y1: 0, x2: 1, y2: 1 },
            stops: [
              [0, '#64003e'],
              [1, '#b12a47']
            ]
          }
        },
        {
          name: 'Remaining',
          y: 100 - progressPercentage,
          color: '#e5e7eb'
        }
      ],
      innerSize: '75%',
      startAngle: -90,
      endAngle: 90,
      dataLabels: {
        enabled: false
      }
    }],
    legend: { enabled: false },
    credits: { enabled: false },
    plotOptions: {
      pie: {
        allowPointSelect: false,
        cursor: 'default',
        showInLegend: false,
        borderWidth: 3,
        borderColor: '#ffffff',
        states: {
          hover: {
            enabled: false
          }
        }
      }
    },
    tooltip: {
      enabled: false
    }
  };

  // Handler functions for metric cards
  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/10 to-primary/5 p-6">
      {/* Enhanced Header Section */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0">
        <div className="space-y-2">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-primary to-primary-light bg-clip-text text-transparent">
            Admin Dashboard
          </h1>
          <p className="text-muted-foreground text-lg">
            Comprehensive system overview and management center
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Avatar className="w-12 h-12 border-2 border-gray-200">
            <AvatarImage src={administrator.avatar} alt={administrator.name} />
            <AvatarFallback className="bg-gradient-to-br from-primary to-primary-light text-primary-foreground text-sm font-bold">
              {administrator.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div className="space-y-0">
            <p className="font-semibold text-foreground text-lg">{administrator.name}</p>
            <p className="text-sm text-muted-foreground">{administrator.role}</p>
          </div>
        </div>
      </div>


      {/* Training Metrics - Top 4 Boxes */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
        {dashboardData.trainingMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            id={metric.id}
            title={metric.title}
            value={metric.value}
            unit={metric.unit}
            target={metric.target}
            targetPercentage={metric.targetPercentage}
            trend={metric.trend}
            isImproving={metric.isImproving}
            mtdVariationFromPY={metric.mtdVariationFromPY}
            mtdCurrentValue={metric.mtdCurrentValue}
            mtdPreviousYearValue={metric.mtdPreviousYearValue}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
            helpText={metric.helpText}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = dashboardData.trainingMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <div className="mb-8">
            <MetricDetails
              title={selectedMetric.title}
              value={selectedMetric.value}
              unit={selectedMetric.unit}
              targetPercentage={selectedMetric.targetPercentage}
              trend={selectedMetric.trend}
              isImproving={selectedMetric.isImproving}
              selectedPeriod={getSelectedPeriod(openDetailsId)}
            />
          </div>
        ) : null;
      })()}

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">

        {/* Enhanced Card 1: Total Number of Active Learners */}
        <Card className="group relative overflow-hidden bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 hover:border-primary/30 hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardHeader className="pb-4 relative z-10">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors duration-300">
                  <Users className="w-6 h-6 text-primary" />
                </div>
                <span className="font-semibold">Active Learners</span>
              </div>
              <TrendingUp className="w-5 h-5 text-green-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-5xl font-bold text-primary mb-2">
                  {dashboardData.totalActiveLearners.current}
                </div>
                <div className="text-sm text-muted-foreground">
                  of {dashboardData.totalActiveLearners.total} total
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span className="font-medium">
                    {Math.round((dashboardData.totalActiveLearners.current / dashboardData.totalActiveLearners.total) * 100)}%
                  </span>
                </div>
                <Progress
                  value={(dashboardData.totalActiveLearners.current / dashboardData.totalActiveLearners.total) * 100}
                  className="h-2"
                />
              </div>

              {/* Speedometer Chart */}
              <div className="relative bg-gradient-to-br from-gray-50 to-white rounded-xl p-4 border border-gray-100">
                <div className="h-40">
                  <HighchartsReact
                    highcharts={Highcharts}
                    options={speedometerOptions}
                  />
                </div>
                {/* Center text overlay */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center mt-6">
                    <div className="text-3xl font-bold text-primary mb-1">
                      {Math.round((dashboardData.totalActiveLearners.current / dashboardData.totalActiveLearners.total) * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground font-medium">
                      ACTIVE RATE
                    </div>
                  </div>
                </div>
                {/* Speedometer indicators */}
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex items-center gap-4 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 rounded-full bg-primary"></div>
                    <span className="text-muted-foreground">Active</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 rounded-full bg-gray-300"></div>
                    <span className="text-muted-foreground">Remaining</span>
                  </div>
                </div>
              </div>


            </div>
          </CardContent>
        </Card>

        {/* Enhanced Card 2: Total Number of People Onboarded */}
        <Card className="group relative overflow-hidden bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 hover:border-primary/30 hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardHeader className="pb-4 relative z-10">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/10 rounded-lg group-hover:bg-blue-500/20 transition-colors duration-300">
                  <UserPlus className="w-6 h-6 text-blue-600" />
                </div>
                <span className="font-semibold">Onboarded (YTD)</span>
              </div>
              <Calendar className="w-5 h-5 text-blue-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-5xl font-bold text-blue-600 mb-2">
                  {dashboardData.totalOnboarded}
                </div>
                <div className="text-sm text-muted-foreground">
                  new employees this year
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 text-center mb-4">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">+15</div>
                  <div className="text-xs text-blue-500">This month</div>
                </div>
                <div className="p-2 bg-green-50 rounded-lg">
                  <div className="text-lg font-bold text-green-600">98%</div>
                  <div className="text-xs text-green-500">Success rate</div>
                </div>
              </div>

              {/* Monthly Onboarding Trend Chart */}
              <div className="bg-white/50 rounded-lg p-3 border border-blue-100">
                <h4 className="text-sm font-semibold text-gray-700 mb-2">Monthly Onboarding Trend</h4>
                <div className="h-48">
                  <HighchartsReact
                    highcharts={Highcharts}
                    options={onboardingTrendOptions}
                  />
                </div>
              </div>


            </div>
          </CardContent>
        </Card>

        {/* Enhanced Card 3: Training Hours (YTD) */}
        <Card className="group relative overflow-hidden bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 hover:border-primary/30 hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardHeader className="pb-4 relative z-10">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-500/10 rounded-lg group-hover:bg-orange-500/20 transition-colors duration-300">
                  <Clock className="w-6 h-6 text-orange-600" />
                </div>
                <span className="font-semibold">Training Hours (YTD)</span>
              </div>
              <BarChart3 className="w-5 h-5 text-orange-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="space-y-4">
              <div className="h-52">
                <HighchartsReact
                  highcharts={Highcharts}
                  options={trainingHoursPieOptions}
                />
              </div>
              <div className="grid grid-cols-1 gap-2 text-sm">
                <div className="flex items-center justify-between p-2 bg-primary/5 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#64003e]"></div>
                    <span className="font-medium">Self Paced</span>
                  </div>
                  <span className="font-bold text-primary">{dashboardData.trainingHours.selfPaced}h</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-primary/5 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#8b1538]"></div>
                    <span className="font-medium">Instructor Led</span>
                  </div>
                  <span className="font-bold text-primary">{dashboardData.trainingHours.instructorLed}h</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-primary/5 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#b12a47]"></div>
                    <span className="font-medium">Virtual</span>
                  </div>
                  <span className="font-bold text-primary">{dashboardData.trainingHours.virtual}h</span>
                </div>
              </div>

            </div>
          </CardContent>
        </Card>

        {/* Enhanced Card 4: Group wide Learning Coverage */}
        <Card className="group relative overflow-hidden bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 hover:border-primary/30 hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardHeader className="pb-4 relative z-10">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500/10 rounded-lg group-hover:bg-purple-500/20 transition-colors duration-300">
                  <Building2 className="w-6 h-6 text-purple-600" />
                </div>
                <span className="font-semibold">Group Learning Coverage</span>
              </div>
              <Globe className="w-5 h-5 text-purple-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="space-y-4">
              <div className="grid gap-3">
                {dashboardData.groupLearningCoverage.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 rounded-full bg-purple-500 animate-pulse"></div>
                      <span className="font-medium text-purple-700">{item.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-sm font-bold text-purple-600">{item.completion}%</div>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    </div>
                  </div>
                ))}
              </div>
              <div className="pt-2">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-muted-foreground">Overall Coverage</span>
                  <span className="font-bold text-purple-600">90%</span>
                </div>
                <Progress value={90} className="h-2" />
              </div>

            </div>
          </CardContent>
        </Card>

        {/* Enhanced Card 5: Active Life Support Certifications */}
        <Card className="group relative overflow-hidden bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 hover:border-primary/30 hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardHeader className="pb-4 relative z-10">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/10 rounded-lg group-hover:bg-green-500/20 transition-colors duration-300">
                  <Award className="w-6 h-6 text-green-600" />
                </div>
                <span className="font-semibold">Life Support Certifications</span>
              </div>
              <Heart className="w-5 h-5 text-green-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="space-y-4">
              <div className="h-52">
                <HighchartsReact
                  highcharts={Highcharts}
                  options={certificationsPieOptions}
                />
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#64003e]"></div>
                    <span className="font-medium">BLS</span>
                  </div>
                  <span className="font-bold text-green-600">{dashboardData.activeCertifications.bls}%</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#8b1538]"></div>
                    <span className="font-medium">ACLS</span>
                  </div>
                  <span className="font-bold text-green-600">{dashboardData.activeCertifications.acls}%</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#b12a47]"></div>
                    <span className="font-medium">PALS</span>
                  </div>
                  <span className="font-bold text-green-600">{dashboardData.activeCertifications.pals}%</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#d63f56]"></div>
                    <span className="font-medium">Heart Saver</span>
                  </div>
                  <span className="font-bold text-green-600">{dashboardData.activeCertifications.heartSaver}%</span>
                </div>
              </div>

            </div>
          </CardContent>
        </Card>

        {/* Enhanced Card 6: Learners Feedback */}
        <Card className="group relative overflow-hidden bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 hover:border-primary/30 hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardHeader className="pb-4 relative z-10">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-500/10 rounded-lg group-hover:bg-yellow-500/20 transition-colors duration-300">
                  <MessageSquare className="w-6 h-6 text-yellow-600" />
                </div>
                <span className="font-semibold">Learners Feedback (YTD)</span>
              </div>
              <Star className="w-5 h-5 text-yellow-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="space-y-4">
              <div className="text-center p-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl border border-yellow-200">
                <div className="text-6xl font-bold text-yellow-600 mb-2">
                  {dashboardData.learnersFeedback.nps}
                </div>
                <div className="text-lg font-semibold text-yellow-700">NPS Score</div>
                <div className="text-sm text-yellow-600">Net Promoter Score</div>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-foreground flex items-center gap-2">
                  <Target className="w-4 h-4 text-yellow-500" />
                  Top Programs:
                </h4>
                {dashboardData.learnersFeedback.topPrograms.map((program, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </div>
                      <span className="text-sm font-medium text-yellow-700">{program}</span>
                    </div>
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                  </div>
                ))}
              </div>

            </div>
          </CardContent>
        </Card>

        {/* Enhanced Card 7: Program Categories (YTD) */}
        <Card className="group relative overflow-hidden bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 hover:border-primary/30 hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardHeader className="pb-4 relative z-10">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-indigo-500/10 rounded-lg group-hover:bg-indigo-500/20 transition-colors duration-300">
                  <BarChart3 className="w-6 h-6 text-indigo-600" />
                </div>
                <span className="font-semibold">Program Categories (YTD)</span>
              </div>
              <TrendingUp className="w-5 h-5 text-indigo-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="space-y-4">
              <div className="h-52">
                <HighchartsReact
                  highcharts={Highcharts}
                  options={programCategoriesChartOptions}
                />
              </div>
              <div className="grid grid-cols-1 gap-2 text-sm">
                <div className="flex items-center justify-between p-2 bg-indigo-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#64003e]"></div>
                    <span className="font-medium">Clinical</span>
                  </div>
                  <span className="font-bold text-indigo-600">{dashboardData.programCategories.clinical}</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-indigo-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#8b1538]"></div>
                    <span className="font-medium">Technical</span>
                  </div>
                  <span className="font-bold text-indigo-600">{dashboardData.programCategories.technical}</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-indigo-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-[#b12a47]"></div>
                    <span className="font-medium">Behavioural</span>
                  </div>
                  <span className="font-bold text-indigo-600">{dashboardData.programCategories.behavioural}</span>
                </div>
              </div>

            </div>
          </CardContent>
        </Card>

        {/* Enhanced Card 8: Overall Training Programs */}
        <Card className="group relative overflow-hidden bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 hover:border-primary/30 hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
          <div className="absolute inset-0 bg-gradient-to-br from-teal-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <CardHeader className="pb-4 relative z-10">
            <CardTitle className="flex items-center justify-between text-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-teal-500/10 rounded-lg group-hover:bg-teal-500/20 transition-colors duration-300">
                  <BookOpen className="w-6 h-6 text-teal-600" />
                </div>
                <span className="font-semibold">Overall Training Programs</span>
              </div>
              <Zap className="w-5 h-5 text-teal-500" />
            </CardTitle>
          </CardHeader>
          <CardContent className="relative z-10">
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-teal-50 to-teal-100 rounded-lg border border-teal-200">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 rounded-full bg-teal-500"></div>
                    <span className="font-medium text-teal-700">Self Paced Training</span>
                  </div>
                  <div className="text-lg font-bold text-teal-600">{dashboardData.overallTrainingPrograms.selfPaced}</div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-teal-50 to-teal-100 rounded-lg border border-teal-200">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 rounded-full bg-teal-600"></div>
                    <span className="font-medium text-teal-700">Instructor Led Training</span>
                  </div>
                  <div className="text-lg font-bold text-teal-600">{dashboardData.overallTrainingPrograms.instructorLed}</div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-teal-50 to-teal-100 rounded-lg border border-teal-200">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 rounded-full bg-teal-700"></div>
                    <span className="font-medium text-teal-700">Virtual Training</span>
                  </div>
                  <div className="text-lg font-bold text-teal-600">{dashboardData.overallTrainingPrograms.virtual}</div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-teal-50 to-teal-100 rounded-lg border border-teal-200">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 rounded-full bg-teal-800"></div>
                    <span className="font-medium text-teal-700">On the Job Training</span>
                  </div>
                  <div className="text-lg font-bold text-teal-600">{dashboardData.overallTrainingPrograms.onTheJob}</div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-teal-50 to-teal-100 rounded-lg border border-teal-200">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 rounded-full bg-teal-900"></div>
                    <span className="font-medium text-teal-700">Hybrid Training</span>
                  </div>
                  <div className="text-lg font-bold text-teal-600">{dashboardData.overallTrainingPrograms.hybrid}</div>
                </div>
              </div>

            </div>
          </CardContent>
        </Card>

      </div>

      {/* Recent Activity Section */}
      <div className="mt-8">
        <Card className="bg-gradient-to-br from-card to-card/50 border-2 border-primary/10 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Activity className="w-6 h-6 text-primary" />
              </div>
              Recent System Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {dashboardData.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border border-primary/20">
                  <div>
                    <div className="font-semibold text-foreground">{activity.action}</div>
                    <div className="text-sm text-muted-foreground">{activity.time}</div>
                  </div>
                  <div className="text-2xl font-bold text-primary">{activity.count}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>



      {/* Footer */}
      <div className="mt-8 text-center">
        <p className="text-muted-foreground text-sm">
          Last updated: {new Date().toLocaleString()} | System Status:
          <span className="text-green-600 font-medium ml-1">All Systems Operational</span>
        </p>
      </div>
    </div>
  );
};

export default AdministratorDashboard;
