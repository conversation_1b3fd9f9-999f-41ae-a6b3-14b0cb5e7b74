import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Users, AlertCircle } from 'lucide-react';

export interface TrainingProgram {
  name: string;
  completed: number;
  total: number;
  priority?: 'high' | 'medium' | 'low';
}

export interface TrainingWiseCoverageProps {
  programs: TrainingProgram[];
}

const TrainingWiseCoverage: React.FC<TrainingWiseCoverageProps> = ({ programs }) => {
  // Sort programs by pending participants (descending order)
  const sortedPrograms = [...programs]
    .sort((a, b) => (b.total - b.completed) - (a.total - a.completed))
    .slice(0, 5); // Top 5 programs

  const getPriorityColor = (priority: string = 'medium') => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-muted-foreground">
          Top 5 Programs by Pending Participants
        </h3>
        <Users className="w-4 h-4 text-muted-foreground" />
      </div>
      
      {sortedPrograms.map((program, index) => {
        const completionPercentage = Math.round((program.completed / program.total) * 100);
        const pendingCount = program.total - program.completed;
        
        return (
          <div key={program.name} className="p-4 rounded-lg border bg-card hover:shadow-sm transition-shadow">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-sm">{program.name}</span>
                  {program.priority && (
                    <Badge variant="outline" className={`text-xs ${getPriorityColor(program.priority)}`}>
                      {program.priority}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>{program.completed} / {program.total} completed</span>
                  <span className="flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {pendingCount} pending
                  </span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-primary">{completionPercentage}%</div>
                <div className="text-xs text-muted-foreground">completion</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Progress 
                value={completionPercentage} 
                className="h-2"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Rank #{index + 1}</span>
                <span>{pendingCount} participants needed</span>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TrainingWiseCoverage;
