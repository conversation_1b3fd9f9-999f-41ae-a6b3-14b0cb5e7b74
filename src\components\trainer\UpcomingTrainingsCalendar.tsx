import React, { useState } from 'react';
import { Calendar, ChevronLeft, ChevronRight, Clock, Users, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export interface TrainingEvent {
  id: string;
  title: string;
  date: Date;
  time: string;
  duration: string;
  participants: number;
  location: string;
  type: 'instructor-led' | 'virtual' | 'hybrid' | 'assessment';
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
}

export interface UpcomingTrainingsCalendarProps {
  events: TrainingEvent[];
}

const UpcomingTrainingsCalendar: React.FC<UpcomingTrainingsCalendarProps> = ({ events }) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'instructor-led': return 'bg-blue-500';
      case 'virtual': return 'bg-green-500';
      case 'hybrid': return 'bg-purple-500';
      case 'assessment': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-500';
      case 'in-progress': return 'bg-yellow-500';
      case 'completed': return 'bg-green-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getMonthDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Start from the first Sunday of the calendar grid
    const startDate = new Date(firstDay);
    startDate.setDate(firstDay.getDate() - firstDay.getDay());

    // Generate 42 days (6 weeks) for the calendar grid
    const days = [];
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      days.push(date);
    }

    return days;
  };

  const getEventsForDate = (date: Date) => {
    return events.filter(event =>
      event.date.toDateString() === date.toDateString()
    );
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const monthDays = getMonthDays();
  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-primary" />
          <h3 className="font-semibold text-sm">Upcoming Trainings</h3>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('prev')}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <span className="text-sm font-medium min-w-[140px] text-center">
            {currentDate.toLocaleDateString('en-US', {
              month: 'long',
              year: 'numeric'
            })}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('next')}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Month Calendar */}
      <div className="border rounded-lg overflow-hidden">
        {/* Week Day Headers */}
        <div className="grid grid-cols-7 bg-muted/50">
          {weekDays.map((day) => (
            <div key={day} className="p-3 text-center text-sm font-medium text-muted-foreground border-r last:border-r-0">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7">
          {monthDays.map((day, index) => {
            const dayEvents = getEventsForDate(day);
            const isCurrentMonthDay = isCurrentMonth(day);
            const isTodayDay = isToday(day);

            return (
              <div
                key={index}
                className={`min-h-[100px] p-2 border-r border-b last:border-r-0 ${
                  !isCurrentMonthDay ? 'bg-muted/20 text-muted-foreground' : 'bg-background'
                } hover:bg-muted/30 transition-colors`}
              >
                {/* Day Number */}
                <div className={`text-sm font-medium mb-1 ${
                  isTodayDay
                    ? 'w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs'
                    : ''
                }`}>
                  {day.getDate()}
                </div>
              
                {/* Events for this day */}
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event) => (
                    <div
                      key={event.id}
                      className={`text-xs p-1 rounded text-white cursor-pointer hover:opacity-80 transition-opacity ${getTypeColor(event.type)}`}
                      title={`${event.title} - ${event.time} (${event.participants} participants)`}
                    >
                      <div className="font-medium truncate">
                        {event.title}
                      </div>
                      <div className="text-xs opacity-90">
                        {event.time}
                      </div>
                    </div>
                  ))}

                  {dayEvents.length > 3 && (
                    <div className="text-xs text-muted-foreground text-center py-1">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Monthly Summary */}
      <div className="space-y-4 pt-4 border-t mt-4">
        {/* Main Statistics */}
        <div className="grid grid-cols-4 gap-3">
          <div className="text-center p-3 bg-primary/5 rounded-lg">
            <div className="text-lg font-bold text-primary">
              {events.filter(e => e.status === 'scheduled').length}
            </div>
            <div className="text-xs text-muted-foreground">Scheduled</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-lg font-bold text-green-600">
              {events.reduce((sum, e) => sum + e.participants, 0)}
            </div>
            <div className="text-xs text-muted-foreground">Participants</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-lg font-bold text-blue-600">
              {new Set(events.map(e => e.type)).size}
            </div>
            <div className="text-xs text-muted-foreground">Types</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-lg font-bold text-orange-600">
              {events.filter(e => {
                const eventDate = new Date(e.date);
                return eventDate.getMonth() === currentDate.getMonth() &&
                       eventDate.getFullYear() === currentDate.getFullYear();
              }).length}
            </div>
            <div className="text-xs text-muted-foreground">This Month</div>
          </div>
        </div>

        {/* Training Type Legend */}
        <div className="space-y-2">
          <h4 className="text-sm font-semibold text-muted-foreground">Training Types</h4>
          <div className="flex flex-wrap gap-2">
            <div className="flex items-center gap-2 text-xs">
              <div className="w-3 h-3 rounded bg-blue-500"></div>
              <span>Instructor-Led</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <div className="w-3 h-3 rounded bg-green-500"></div>
              <span>Virtual</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <div className="w-3 h-3 rounded bg-purple-500"></div>
              <span>Hybrid</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <div className="w-3 h-3 rounded bg-orange-500"></div>
              <span>Assessment</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex-1 text-xs">
            <Calendar className="w-3 h-3 mr-1" />
            View Details
          </Button>
          <Button variant="outline" size="sm" className="flex-1 text-xs">
            Add Training
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UpcomingTrainingsCalendar;
