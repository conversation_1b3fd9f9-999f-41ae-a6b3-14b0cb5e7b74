import React, { useState } from 'react';
import { Calendar, ChevronLeft, ChevronRight, Clock, Users, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export interface TrainingEvent {
  id: string;
  title: string;
  date: Date;
  time: string;
  duration: string;
  participants: number;
  location: string;
  type: 'instructor-led' | 'virtual' | 'hybrid' | 'assessment';
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
}

export interface UpcomingTrainingsCalendarProps {
  events: TrainingEvent[];
}

const UpcomingTrainingsCalendar: React.FC<UpcomingTrainingsCalendarProps> = ({ events }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'week' | 'month'>('week');

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'instructor-led': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'virtual': return 'bg-green-100 text-green-800 border-green-200';
      case 'hybrid': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'assessment': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-500';
      case 'in-progress': return 'bg-yellow-500';
      case 'completed': return 'bg-green-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getWeekDays = () => {
    const startOfWeek = new Date(currentDate);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day;
    startOfWeek.setDate(diff);

    return Array.from({ length: 7 }, (_, i) => {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      return date;
    });
  };

  const getEventsForDate = (date: Date) => {
    return events.filter(event => 
      event.date.toDateString() === date.toDateString()
    );
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentDate(newDate);
  };

  const weekDays = getWeekDays();

  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-primary" />
          <h3 className="font-semibold text-sm">Upcoming Trainings</h3>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateWeek('prev')}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <span className="text-sm font-medium min-w-[120px] text-center">
            {currentDate.toLocaleDateString('en-US', { 
              month: 'long', 
              year: 'numeric' 
            })}
          </span>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateWeek('next')}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Week View */}
      <div className="grid grid-cols-7 gap-2">
        {weekDays.map((day, index) => {
          const dayEvents = getEventsForDate(day);
          const isToday = day.toDateString() === new Date().toDateString();
          
          return (
            <div key={index} className="space-y-2">
              {/* Day Header */}
              <div className={`text-center p-2 rounded-lg ${
                isToday ? 'bg-primary text-primary-foreground' : 'bg-muted'
              }`}>
                <div className="text-xs font-medium">
                  {day.toLocaleDateString('en-US', { weekday: 'short' })}
                </div>
                <div className="text-sm font-bold">
                  {day.getDate()}
                </div>
              </div>
              
              {/* Events for this day */}
              <div className="space-y-1 min-h-[200px]">
                {dayEvents.map((event) => (
                  <div
                    key={event.id}
                    className="p-2 rounded border bg-card hover:shadow-sm transition-shadow cursor-pointer"
                  >
                    <div className="flex items-start justify-between mb-1">
                      <div className={`w-2 h-2 rounded-full ${getStatusColor(event.status)}`}></div>
                      <Badge variant="outline" className={`text-xs ${getTypeColor(event.type)}`}>
                        {event.type.replace('-', ' ')}
                      </Badge>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-xs font-medium line-clamp-2">
                        {event.title}
                      </div>
                      
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        {event.time}
                      </div>
                      
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Users className="w-3 h-3" />
                        {event.participants}
                      </div>
                      
                      {event.location && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <MapPin className="w-3 h-3" />
                          <span className="truncate">{event.location}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t">
        <div className="text-center">
          <div className="text-lg font-bold text-primary">
            {events.filter(e => e.status === 'scheduled').length}
          </div>
          <div className="text-xs text-muted-foreground">Scheduled</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-primary">
            {events.reduce((sum, e) => sum + e.participants, 0)}
          </div>
          <div className="text-xs text-muted-foreground">Total Participants</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-primary">
            {new Set(events.map(e => e.type)).size}
          </div>
          <div className="text-xs text-muted-foreground">Training Types</div>
        </div>
      </div>
    </div>
  );
};

export default UpcomingTrainingsCalendar;
