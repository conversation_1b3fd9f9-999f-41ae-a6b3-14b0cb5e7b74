import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import {
  Gauge,
  PieChart,
  Calendar as CalendarIcon,
  TrendingUp,
  Star,
  Users,
  BookOpen,
  Award
} from 'lucide-react';

// Import trainer-specific components
import ActiveLifeSupportSpeedometers from '@/components/trainer/ActiveLifeSupportSpeedometers';
import TrainingWiseCoverage from '@/components/trainer/TrainingWiseCoverage';
import TrainingHoursPieChart from '@/components/trainer/TrainingHoursPieChart';
import LearnersFeedback from '@/components/trainer/LearnersFeedback';
import UpcomingTrainingsCalendar from '@/components/trainer/UpcomingTrainingsCalendar';
import TrainingCoverageTrends from '@/components/trainer/TrainingCoverageTrends';

export const TrainerDashboard: React.FC = () => {
  const { user } = useAuth();

  // Mock data for trainer dashboard
  const trainer = {
    name: user?.name || "Dr. <PERSON>",
    role: "Senior Training Coordinator",
    avatar: user?.avatar || "https://images.unsplash.com/photo-**********-2b71ea197ec2?q=80&w=1000&auto=format&fit=crop",
    department: user?.department || "Medical Education Department"
  };

  // Mock data for Active Life Support Certifications
  const lifeSupportCertifications = [
    { name: 'BLS', certified: 85, total: 100, color: '#10b981' },
    { name: 'ACLS', certified: 72, total: 90, color: '#3b82f6' },
    { name: 'PALS', certified: 45, total: 60, color: '#f59e0b' },
    { name: 'Heart Saver', certified: 120, total: 150, color: '#ef4444' }
  ];

  // Mock data for Training Programs Coverage
  const trainingPrograms = [
    { name: 'IPSG', completed: 456, total: 500, priority: 'high' as const },
    { name: 'Infection Control', completed: 46, total: 80, priority: 'high' as const },
    { name: 'Nursing Quality', completed: 30, total: 57, priority: 'medium' as const },
    { name: 'Patient Safety', completed: 65, total: 85, priority: 'medium' as const },
    { name: 'Emergency Response', completed: 40, total: 55, priority: 'low' as const }
  ];

  // Mock data for Training Hours (YTD)
  const trainingHoursData = {
    selfPaced: 1250,
    instructorLed: 980,
    virtual: 750,
    onTheJob: 650,
    hybrid: 420
  };

  // Mock data for Learners Feedback
  const learnersFeedbackData = {
    npsScore: 94,
    topPrograms: [
      {
        name: 'Advanced Cardiac Life Support',
        rating: 4.8,
        participants: 156,
        feedback: 'Excellent hands-on training with real-world scenarios',
        category: 'Clinical'
      },
      {
        name: 'Patient Communication Skills',
        rating: 4.7,
        participants: 203,
        feedback: 'Very practical and immediately applicable',
        category: 'Soft Skills'
      },
      {
        name: 'Infection Prevention Protocols',
        rating: 4.6,
        participants: 189,
        feedback: 'Clear guidelines and comprehensive coverage',
        category: 'Safety'
      }
    ]
  };

  // Mock data for Upcoming Training Events
  const upcomingTrainingEvents = [
    {
      id: '1',
      title: 'BLS Certification',
      date: new Date(2025, 0, 13), // January 13, 2025
      time: '09:00 AM',
      duration: '4 hours',
      participants: 15,
      location: 'Training Room A',
      type: 'instructor-led' as const,
      status: 'scheduled' as const
    },
    {
      id: '2',
      title: 'ACLS Recertification',
      date: new Date(2025, 0, 14), // January 14, 2025
      time: '02:00 PM',
      duration: '6 hours',
      participants: 12,
      location: 'Simulation Lab',
      type: 'hybrid' as const,
      status: 'scheduled' as const
    },
    {
      id: '3',
      title: 'Infection Control Workshop',
      date: new Date(2025, 0, 15), // January 15, 2025
      time: '10:00 AM',
      duration: '2 hours',
      participants: 25,
      location: 'Virtual',
      type: 'virtual' as const,
      status: 'scheduled' as const
    },
    {
      id: '4',
      title: 'Patient Safety Assessment',
      date: new Date(2025, 0, 16), // January 16, 2025
      time: '01:00 PM',
      duration: '3 hours',
      participants: 20,
      location: 'Assessment Center',
      type: 'assessment' as const,
      status: 'scheduled' as const
    },
    {
      id: '5',
      title: 'PALS Training',
      date: new Date(2025, 0, 17), // January 17, 2025
      time: '08:30 AM',
      duration: '8 hours',
      participants: 18,
      location: 'Training Room B',
      type: 'instructor-led' as const,
      status: 'scheduled' as const
    },
    {
      id: '6',
      title: 'CPR Refresher',
      date: new Date(2025, 0, 13), // January 13, 2025
      time: '02:00 PM',
      duration: '2 hours',
      participants: 30,
      location: 'Main Hall',
      type: 'instructor-led' as const,
      status: 'scheduled' as const
    },
    {
      id: '7',
      title: 'Emergency Response Drill',
      date: new Date(2025, 0, 14), // January 14, 2025
      time: '11:00 AM',
      duration: '1 hour',
      participants: 45,
      location: 'Hospital Wide',
      type: 'assessment' as const,
      status: 'scheduled' as const
    },
    {
      id: '8',
      title: 'Medication Safety',
      date: new Date(2025, 0, 15), // January 15, 2025
      time: '03:00 PM',
      duration: '3 hours',
      participants: 22,
      location: 'Virtual',
      type: 'virtual' as const,
      status: 'scheduled' as const
    },
    {
      id: '9',
      title: 'Hand Hygiene Training',
      date: new Date(2025, 0, 16), // January 16, 2025
      time: '09:00 AM',
      duration: '1.5 hours',
      participants: 35,
      location: 'Training Room C',
      type: 'instructor-led' as const,
      status: 'scheduled' as const
    },
    {
      id: '10',
      title: 'Communication Skills',
      date: new Date(2025, 0, 17), // January 17, 2025
      time: '02:30 PM',
      duration: '4 hours',
      participants: 16,
      location: 'Conference Room',
      type: 'hybrid' as const,
      status: 'scheduled' as const
    }
  ];

  // Mock data for Training Coverage Trends
  const coverageTrendsPrograms = [
    {
      id: 'ipsg',
      name: 'IPSG (International Patient Safety Goals)',
      color: '#3b82f6',
      category: 'Safety',
      monthlyData: [
        { month: 'Jan', employees: 45, target: 50 },
        { month: 'Feb', employees: 52, target: 55 },
        { month: 'Mar', employees: 48, target: 50 },
        { month: 'Apr', employees: 61, target: 60 },
        { month: 'May', employees: 58, target: 65 },
        { month: 'Jun', employees: 67, target: 70 },
        { month: 'Jul', employees: 72, target: 75 },
        { month: 'Aug', employees: 69, target: 75 },
        { month: 'Sep', employees: 78, target: 80 },
        { month: 'Oct', employees: 82, target: 85 },
        { month: 'Nov', employees: 79, target: 85 },
        { month: 'Dec', employees: 88, target: 90 }
      ]
    },
    {
      id: 'infection-control',
      name: 'Infection Control',
      color: '#10b981',
      category: 'Clinical',
      monthlyData: [
        { month: 'Jan', employees: 32, target: 35 },
        { month: 'Feb', employees: 38, target: 40 },
        { month: 'Mar', employees: 35, target: 40 },
        { month: 'Apr', employees: 42, target: 45 },
        { month: 'May', employees: 48, target: 50 },
        { month: 'Jun', employees: 45, target: 50 },
        { month: 'Jul', employees: 52, target: 55 },
        { month: 'Aug', employees: 49, target: 55 },
        { month: 'Sep', employees: 58, target: 60 },
        { month: 'Oct', employees: 62, target: 65 },
        { month: 'Nov', employees: 59, target: 65 },
        { month: 'Dec', employees: 68, target: 70 }
      ]
    },
    {
      id: 'nursing-quality',
      name: 'Nursing Quality',
      color: '#f59e0b',
      category: 'Quality',
      monthlyData: [
        { month: 'Jan', employees: 28, target: 30 },
        { month: 'Feb', employees: 31, target: 35 },
        { month: 'Mar', employees: 29, target: 35 },
        { month: 'Apr', employees: 36, target: 40 },
        { month: 'May', employees: 38, target: 40 },
        { month: 'Jun', employees: 42, target: 45 },
        { month: 'Jul', employees: 44, target: 45 },
        { month: 'Aug', employees: 41, target: 45 },
        { month: 'Sep', employees: 47, target: 50 },
        { month: 'Oct', employees: 52, target: 55 },
        { month: 'Nov', employees: 49, target: 55 },
        { month: 'Dec', employees: 56, target: 60 }
      ]
    }
  ];

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Trainers Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Monitor training coverage, certification status, and program effectiveness
          </p>
        </div>

        <div className="flex items-center gap-3">
          <div className="text-right">
            <div className="font-semibold text-foreground">{trainer.name}</div>
            <div className="text-sm text-muted-foreground">{trainer.role}</div>
            <div className="text-xs text-muted-foreground">{trainer.department}</div>
          </div>
          <Avatar className="h-12 w-12">
            <AvatarImage src={trainer.avatar} alt={trainer.name} />
            <AvatarFallback>{trainer.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
        </div>
      </div>

      {/* First Row - Active Life Support Certifications */}
      <div className="grid grid-cols-1 gap-6">
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Gauge className="w-5 h-5 text-primary" />
              Active Life Support Certifications
            </CardTitle>
            <CardDescription className="text-sm">
              Percentage of staff certified in critical life support programs
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <ActiveLifeSupportSpeedometers certifications={lifeSupportCertifications} />
          </CardContent>
        </Card>
      </div>

      {/* Second Row - Training Wise Coverage and Training Hours (YTD) */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Training Wise Coverage */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <BookOpen className="w-5 h-5 text-primary" />
              Training Wise Coverage
            </CardTitle>
            <CardDescription className="text-sm">
              Top training programs ranked by pending participants
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <TrainingWiseCoverage programs={trainingPrograms} />
          </CardContent>
        </Card>

        {/* Training Hours (YTD) */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <PieChart className="w-5 h-5 text-primary" />
              Training Hours (YTD)
            </CardTitle>
            <CardDescription className="text-sm">
              Total hours spent across different training delivery methods
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <TrainingHoursPieChart data={trainingHoursData} />
          </CardContent>
        </Card>
      </div>

      {/* Third Row - Upcoming Trainings and Training Coverage Trends */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Upcoming Trainings Calendar */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <CalendarIcon className="w-5 h-5 text-primary" />
              Upcoming Trainings
            </CardTitle>
            <CardDescription className="text-sm">
              Weekly calendar view of scheduled training sessions
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <UpcomingTrainingsCalendar events={upcomingTrainingEvents} />
          </CardContent>
        </Card>

        {/* Training Coverage Trends */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <TrendingUp className="w-5 h-5 text-primary" />
              Training Coverage Trends
            </CardTitle>
            <CardDescription className="text-sm">
              Monthly tracking of employees trained by program
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <TrainingCoverageTrends programs={coverageTrendsPrograms} />
          </CardContent>
        </Card>
      </div>

      {/* Fourth Row - Learners Feedback */}
      <div className="grid grid-cols-1 gap-6">
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Star className="w-5 h-5 text-primary" />
              Learners Feedback
            </CardTitle>
            <CardDescription className="text-sm">
              Net Promoter Score and top-rated training programs
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <LearnersFeedback
              npsScore={learnersFeedbackData.npsScore}
              topPrograms={learnersFeedbackData.topPrograms}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TrainerDashboard;
