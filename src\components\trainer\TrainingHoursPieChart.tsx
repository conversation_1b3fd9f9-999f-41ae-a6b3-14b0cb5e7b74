import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

export interface TrainingHoursData {
  selfPaced: number;
  instructorLed: number;
  virtual: number;
  onTheJob: number;
  hybrid: number;
}

export interface TrainingHoursPieChartProps {
  data: TrainingHoursData;
  height?: number;
  showLegend?: boolean;
}

const TrainingHoursPieChart: React.FC<TrainingHoursPieChartProps> = ({ 
  data, 
  height = 280, 
  showLegend = true 
}) => {
  const chartData = [
    { name: 'Self-Paced Training', y: data.selfPaced, color: '#3b82f6' },
    { name: 'Instructor-Led Training', y: data.instructorLed, color: '#10b981' },
    { name: 'Virtual Training', y: data.virtual, color: '#f59e0b' },
    { name: 'On-the-Job Training', y: data.onTheJob, color: '#ef4444' },
    { name: 'Hybrid Training', y: data.hybrid, color: '#8b5cf6' }
  ];

  const totalHours = Object.values(data).reduce((sum, hours) => sum + hours, 0);

  const options: Highcharts.Options = {
    chart: {
      type: 'pie',
      height,
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'Inter, system-ui, sans-serif'
      }
    },
    title: { text: null },
    series: [{
      type: 'pie',
      name: 'Training Hours',
      data: chartData,
      innerSize: '50%',
      dataLabels: {
        enabled: true,
        distance: 15,
        format: '{point.name}<br/><b>{point.y}h</b><br/>({point.percentage:.1f}%)',
        style: {
          fontWeight: '600',
          color: '#334155',
          fontSize: '10px',
          textOutline: 'none'
        }
      },
      animation: {
        duration: 800
      },
      borderWidth: 2,
      borderColor: '#ffffff'
    }],
    legend: {
      enabled: showLegend,
      align: 'center',
      verticalAlign: 'bottom',
      layout: 'horizontal',
      itemStyle: {
        fontSize: '11px',
        fontWeight: '500'
      }
    },
    credits: { enabled: false },
    tooltip: {
      pointFormat: '<b>{point.percentage:.1f}%</b><br/>Hours: {point.y}<br/>Total: ' + totalHours + 'h'
    },
    plotOptions: {
      pie: {
        allowPointSelect: true,
        cursor: 'pointer',
        states: {
          hover: {
            halo: {
              size: 5
            }
          }
        }
      },
      series: {
        animation: {
          duration: 800
        }
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="h-[280px]">
        <HighchartsReact highcharts={Highcharts} options={options} />
      </div>
      
      {/* Summary Statistics */}
      <div className="grid grid-cols-2 gap-4 mt-4">
        <div className="text-center p-3 bg-primary/5 rounded-lg">
          <div className="text-2xl font-bold text-primary">{totalHours}h</div>
          <div className="text-xs text-muted-foreground">Total Hours (YTD)</div>
        </div>
        <div className="text-center p-3 bg-secondary/50 rounded-lg">
          <div className="text-2xl font-bold text-secondary-foreground">
            {Math.round(totalHours / 12)}h
          </div>
          <div className="text-xs text-muted-foreground">Avg per Month</div>
        </div>
      </div>
    </div>
  );
};

export default TrainingHoursPieChart;
