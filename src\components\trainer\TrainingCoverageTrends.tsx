import React, { useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Calendar, Users } from 'lucide-react';

export interface MonthlyTrainingData {
  month: string;
  employees: number;
  target?: number;
}

export interface TrainingProgram {
  id: string;
  name: string;
  monthlyData: MonthlyTrainingData[];
  color?: string;
  category?: string;
}

export interface TrainingCoverageTrendsProps {
  programs: TrainingProgram[];
  defaultProgramId?: string;
  timeframe?: 'monthly' | 'quarterly' | 'ytd';
}

const TrainingCoverageTrends: React.FC<TrainingCoverageTrendsProps> = ({ 
  programs, 
  defaultProgramId,
  timeframe = 'monthly'
}) => {
  const [selectedProgramId, setSelectedProgramId] = useState(
    defaultProgramId || programs[0]?.id || ''
  );
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);

  const selectedProgram = programs.find(p => p.id === selectedProgramId);

  const getChartOptions = (): Highcharts.Options => {
    if (!selectedProgram) return {};

    const categories = selectedProgram.monthlyData.map(d => d.month);
    const employeesData = selectedProgram.monthlyData.map(d => d.employees);
    const targetData = selectedProgram.monthlyData.map(d => d.target || 0);

    return {
      chart: {
        type: 'column',
        height: 350,
        backgroundColor: 'transparent',
        style: {
          fontFamily: 'Inter, system-ui, sans-serif'
        }
      },
      title: { text: null },
      xAxis: {
        categories,
        title: {
          text: 'Month'
        },
        labels: {
          style: {
            fontSize: '11px'
          }
        }
      },
      yAxis: {
        title: {
          text: 'Number of Employees'
        },
        min: 0,
        labels: {
          style: {
            fontSize: '11px'
          }
        }
      },
      series: [
        {
          type: 'column',
          name: 'Employees Trained',
          data: employeesData,
          color: selectedProgram.color || '#3b82f6',
          borderRadius: 4,
          animation: {
            duration: 800
          }
        },
        {
          type: 'line',
          name: 'Target',
          data: targetData,
          color: '#ef4444',
          marker: {
            enabled: true,
            radius: 4
          },
          lineWidth: 2,
          dashStyle: 'ShortDash'
        }
      ],
      legend: {
        enabled: true,
        align: 'center',
        verticalAlign: 'bottom'
      },
      credits: { enabled: false },
      tooltip: {
        shared: true,
        formatter: function() {
          let tooltip = `<b>${this.x}</b><br/>`;
          this.points?.forEach(point => {
            tooltip += `<span style="color:${point.color}">\u25CF</span> ${point.series.name}: <b>${point.y}</b><br/>`;
          });
          return tooltip;
        }
      },
      plotOptions: {
        column: {
          borderWidth: 0,
          pointPadding: 0.1,
          groupPadding: 0.1
        },
        series: {
          animation: {
            duration: 800
          }
        }
      }
    };
  };

  const getTotalTrained = () => {
    if (!selectedProgram) return 0;
    return selectedProgram.monthlyData.reduce((sum, data) => sum + data.employees, 0);
  };

  const getAveragePerMonth = () => {
    if (!selectedProgram || selectedProgram.monthlyData.length === 0) return 0;
    return Math.round(getTotalTrained() / selectedProgram.monthlyData.length);
  };

  const getTargetAchievement = () => {
    if (!selectedProgram) return 0;
    const totalTarget = selectedProgram.monthlyData.reduce((sum, data) => sum + (data.target || 0), 0);
    const totalTrained = getTotalTrained();
    return totalTarget > 0 ? Math.round((totalTrained / totalTarget) * 100) : 0;
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-primary" />
          <h3 className="font-semibold text-sm">Training Coverage Trends</h3>
        </div>
        
        <div className="flex gap-2">
          <Select value={selectedProgramId} onValueChange={setSelectedProgramId}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select Program" />
            </SelectTrigger>
            <SelectContent>
              {programs.map((program) => (
                <SelectItem key={program.id} value={program.id}>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: program.color || '#3b82f6' }}
                    ></div>
                    {program.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={selectedTimeframe} onValueChange={(value: any) => setSelectedTimeframe(value)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="ytd">YTD</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Selected Program Info */}
      {selectedProgram && (
        <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
          <div 
            className="w-4 h-4 rounded-full" 
            style={{ backgroundColor: selectedProgram.color || '#3b82f6' }}
          ></div>
          <span className="font-medium text-sm">{selectedProgram.name}</span>
          {selectedProgram.category && (
            <Badge variant="secondary" className="text-xs">
              {selectedProgram.category}
            </Badge>
          )}
        </div>
      )}

      {/* Chart */}
      <div className="h-[350px]">
        <HighchartsReact highcharts={Highcharts} options={getChartOptions()} />
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-3 gap-4 pt-4 border-t">
        <div className="text-center p-3 bg-primary/5 rounded-lg">
          <div className="text-2xl font-bold text-primary">{getTotalTrained()}</div>
          <div className="text-xs text-muted-foreground">Total Trained ({selectedTimeframe})</div>
        </div>
        <div className="text-center p-3 bg-secondary/50 rounded-lg">
          <div className="text-2xl font-bold text-secondary-foreground">{getAveragePerMonth()}</div>
          <div className="text-xs text-muted-foreground">Avg per Month</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{getTargetAchievement()}%</div>
          <div className="text-xs text-muted-foreground">Target Achievement</div>
        </div>
      </div>
    </div>
  );
};

export default TrainingCoverageTrends;
