import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Users, Gauge, <PERSON><PERSON>hart, Calendar as CalendarIcon, ListChecks } from 'lucide-react';
import Speedometer from '@/components/manager/Speedometer';
import TrainingHoursPie from '@/components/manager/TrainingHoursPie';
import TeamTrainingCoverage from '@/components/manager/TeamTrainingCoverage';
import TeamCoverageGauges from '@/components/manager/TeamCoverageGauges';
import ActiveLifeSupportPie from '@/components/manager/ActiveLifeSupportPie';
import UpcomingTeamCalendar, { type TeamEvent } from '@/components/manager/UpcomingTeamCalendar';
import TeamLearningRequestsSlider from '@/components/manager/TeamLearningRequestsSlider';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/contexts/AuthContext';

const ManagerDashboard: React.FC = () => {
  const { user } = useAuth();

  // Use the same profile data structure as ManagerProfile component
  const manager = {
    name: user?.name || "Aisha Khan",
    role: "Department Manager",
    avatar: user?.avatar || "https://images.unsplash.com/photo-1607746882042-944635dfe10e?q=80&w=1000&auto=format&fit=crop",
    department: user?.department || "Nursing Department"
  };

  const activeLearners = { current: 18, total: 20 };

  const teamTrainingCoverage = [
    { program: 'Program 1', completed: 1, total: 20 },
    { program: 'Program 2', completed: 4, total: 20 },
    { program: 'Program 3', completed: 6, total: 20 },
    { program: 'Program 4', completed: 10, total: 20 },
    { program: 'Program 5', completed: 12, total: 20 },
  ];

  const trainingHoursYTD = [
    { name: 'Self-Paced Training', value: 42, color: '#60a5fa' },
    { name: 'Instructor-Led Training', value: 28, color: '#34d399' },
    { name: 'Virtual Training', value: 22, color: '#a78bfa' },
    { name: 'On-the-Job Training', value: 30, color: '#f59e0b' },
    { name: 'Hybrid Training', value: 16, color: '#f472b6' },
  ];

  const teamCoveragePrograms = [
    { name: 'HROB', value: 80, total: 100, color: '#60a5fa' },
    { name: 'Medcare Look', value: 70, total: 100, color: '#34d399' },
    { name: 'GNO', value: 65, total: 100, color: '#a78bfa' },
    { name: 'NFS', value: 55, total: 100, color: '#f59e0b' },
    { name: 'Aster Care', value: 75, total: 100, color: '#f472b6' },
  ];

  const lifeSupportCerts = [
    { name: 'BLS', value: 85, color: '#60a5fa' },
    { name: 'ACLS', value: 78, color: '#34d399' },
    { name: 'PALS', value: 92, color: '#a78bfa' },
    { name: 'Heart Saver', value: 88, color: '#f59e0b' },
  ];

  const upcomingEvents: TeamEvent[] = [
    { date: '2025-08-04', title: 'Medcare Look - Cohort A', type: 'Instructor-Led', time: '10:00 - 12:00', location: 'Room 12', slots: 5 },
    { date: '2025-08-09', title: 'GNO - Webinar', type: 'Virtual', time: '14:00 - 15:30' },
    { date: '2025-08-14', title: 'NFS - Lab', type: 'On-the-Job', time: '09:00 - 11:00' },
    { date: '2025-08-04', title: 'Medcare Look - Cohort B', type: 'Hybrid', time: '13:00 - 15:00', location: 'Room 6' },
  ];

  const requests = [
    { days: 5, name: 'Hanae Abdulhafiz Seid', program: 'Medcare Look', date: '04.Aug.2025', urgency: 'red' as const },
    { days: 10, name: 'Sreeja P. Ajayakumar', program: 'GNO', date: '09.Aug.2025', urgency: 'yellow' as const },
    { days: 15, name: 'Sarita Bhattarai', program: 'NFS', date: '14.Aug.2025', urgency: 'green' as const },
    { days: 3, name: 'Ahmed Al-Rashid', program: 'HROB', date: '02.Aug.2025', urgency: 'red' as const },
    { days: 7, name: 'Maria Santos', program: 'Aster Care', date: '07.Aug.2025', urgency: 'yellow' as const },
    { days: 12, name: 'Priya Sharma', program: 'Medcare Look', date: '12.Aug.2025', urgency: 'yellow' as const },
    { days: 18, name: 'John Mitchell', program: 'GNO', date: '18.Aug.2025', urgency: 'green' as const },
    { days: 20, name: 'Fatima Al-Zahra', program: 'NFS', date: '20.Aug.2025', urgency: 'green' as const },
    { days: 6, name: 'David Chen', program: 'HROB', date: '06.Aug.2025', urgency: 'yellow' as const },
    { days: 25, name: 'Sarah Johnson', program: 'Aster Care', date: '25.Aug.2025', urgency: 'green' as const },
  ];

  const handleApprove = (req: any) => { /* TODO: integrate API */ console.log('approve', req); };
  const handleReject = (req: any) => { /* TODO: integrate API */ console.log('reject', req); };

  return (
    <div className="p-5 space-y-5">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-foreground tracking-tight">Manager Dashboard</h1>
          <p className="text-sm text-muted-foreground">Track team training status, coverage, and upcoming activities</p>
        </div>
        <div className="flex items-center gap-3">
          <Avatar className="w-11 h-11 border border-primary/20">
            <AvatarImage src={manager.avatar} alt={manager.name} />
            <AvatarFallback>
              {manager.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div className="text-right">
            <div className="font-medium text-sm">{manager.name}</div>
            <div className="text-xs text-muted-foreground">{manager.role}</div>
          </div>
        </div>
      </div>

      {/* First Row - Team Learning Coverage (Speedometers) */}
      <Card className="shadow-card hover:shadow-lg transition-shadow">
        <CardHeader className="py-3">
          <CardTitle className="flex items-center gap-2 text-base"><Gauge className="w-5 h-5 text-primary" />Team Learning Coverage</CardTitle>
          <CardDescription className="text-xs">Program coverage across key programs</CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <TeamCoverageGauges items={teamCoveragePrograms} />
        </CardContent>
      </Card>

      {/* Second Row - Total Active Learners, Training Hours (YTD), Active Life Support Certifications */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Total Active Learners - Speedometer */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-3">
            <CardTitle className="flex items-center gap-2 text-base">
              <Users className="w-5 h-5 text-primary" />
              Total Active Learners
            </CardTitle>
            <CardDescription className="text-xs">Current participation out of team total</CardDescription>
          </CardHeader>
          <CardContent className="pt-0 space-y-3">
            <div className="text-center mb-1 text-xs text-muted-foreground">{activeLearners.current}/{activeLearners.total}</div>
            <Speedometer value={activeLearners.current} total={activeLearners.total} color="#60a5fa" />

            {/* Additional Details */}
            <div className="space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">Target</span>
                <span className="font-medium">20 learners</span>
              </div>
              <Progress value={90} className="h-1.5" />
              <div className="grid grid-cols-2 gap-2 text-center">
                <div className="p-2 bg-green-50 rounded-md">
                  <div className="text-sm font-semibold text-green-700">18</div>
                  <div className="text-[10px] text-green-600">Active</div>
                </div>
                <div className="p-2 bg-orange-50 rounded-md">
                  <div className="text-sm font-semibold text-orange-700">2</div>
                  <div className="text-[10px] text-orange-600">Pending</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Training Hours (YTD) - Pie */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-3">
            <CardTitle className="flex items-center gap-2 text-base"><PieChart className="w-5 h-5 text-primary" />Training Hours (YTD)</CardTitle>
            <CardDescription className="text-xs">Breakdown by training type</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <TrainingHoursPie data={trainingHoursYTD} />
          </CardContent>
        </Card>

        {/* Active Life Support Certifications */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-3">
            <CardTitle className="flex items-center gap-2 text-base"><PieChart className="w-5 h-5 text-primary" />Active Life Support Certifications</CardTitle>
            <CardDescription className="text-xs">Clinical teams only</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <ActiveLifeSupportPie data={lifeSupportCerts} />
          </CardContent>
        </Card>
      </div>

      {/* Third Row - Team Training Coverage, Upcoming Team Trainings, Team Learning Requests */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Team Training Coverage - Top pending */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-3">
            <CardTitle className="flex items-center gap-2 text-base"><ListChecks className="w-5 h-5 text-primary" />Team Training Coverage</CardTitle>
            <CardDescription className="text-xs">Top 5 programs by pending participants (descending)</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <TeamTrainingCoverage items={teamTrainingCoverage} top={5} />
          </CardContent>
        </Card>

        {/* Upcoming Team Trainings - Calendar */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-3">
            <CardTitle className="flex items-center gap-2 text-base"><CalendarIcon className="w-5 h-5 text-primary" />Upcoming Team Trainings</CardTitle>
            <CardDescription className="text-xs">Monthly interactive calendar</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <UpcomingTeamCalendar events={upcomingEvents} />
          </CardContent>
        </Card>

        {/* Team Learning Requests - Slider */}
        <Card className="shadow-card hover:shadow-lg transition-shadow">
          <CardHeader className="py-3">
            <CardTitle className="flex items-center gap-2 text-base"><ListChecks className="w-5 h-5 text-primary" />Team Learning Requests</CardTitle>
            <CardDescription className="text-xs">Approve or reject nominations</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <TeamLearningRequestsSlider requests={requests} onApprove={handleApprove} onReject={handleReject} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ManagerDashboard;
