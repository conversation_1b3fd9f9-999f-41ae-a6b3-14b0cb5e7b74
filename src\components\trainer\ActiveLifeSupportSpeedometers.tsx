import React from 'react';
import Speedometer from '@/components/manager/Speedometer';

export interface LifeSupportCertification {
  name: string;
  certified: number;
  total: number;
  color?: string;
}

export interface ActiveLifeSupportSpeedometersProps {
  certifications: LifeSupportCertification[];
}

const ActiveLifeSupportSpeedometers: React.FC<ActiveLifeSupportSpeedometersProps> = ({
  certifications
}) => {
  // Default colors for each certification type
  const defaultColors = {
    'BLS': '#10b981', // green
    'ACLS': '#3b82f6', // blue
    'PALS': '#f59e0b', // amber
    'Heart Saver': '#ef4444' // red
  };

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
      {certifications.map((cert) => (
        <div key={cert.name} className="p-4 rounded-lg border bg-card hover:shadow-md transition-shadow">
          <div className="text-sm font-medium mb-3 text-center text-muted-foreground">
            {cert.name}
          </div>
          <Speedometer 
            value={cert.certified} 
            total={cert.total} 
            color={cert.color || defaultColors[cert.name as keyof typeof defaultColors] || '#3b82f6'}
            height={180}
            activeLabel="Certified"
            remainingLabel="Not Certified"
          />
          <div className="mt-2 text-center">
            <div className="text-xs text-muted-foreground">
              {cert.certified} of {cert.total} staff certified
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ActiveLifeSupportSpeedometers;
