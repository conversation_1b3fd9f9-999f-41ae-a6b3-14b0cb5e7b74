import { useAuth } from "@/contexts/AuthContext";
import {
  <PERSON><PERSON>P<PERSON><PERSON><PERSON>,
  ManagerProfile,
  AdministratorProfile,
  TrainerProfile
} from "@/components/profiles";

const Profile = () => {
  const { user } = useAuth();

  // If no user is authenticated, show a loading or error state
  if (!user) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Profile Not Available</h1>
          <p className="text-muted-foreground">Please log in to view your profile.</p>
        </div>
      </div>
    );
  }

  // Render the appropriate profile component based on user role
  switch (user.role) {
    case 'learner':
      return <LearnerProfile />;
    case 'manager':
      return <ManagerProfile />;
    case 'administrator':
      return <AdministratorProfile />;
    case 'trainer':
      return <TrainerProfile />;
    case 'leadership':
      // For now, leadership uses the manager profile (can be customized later)
      return <ManagerProfile />;
    default:
      // Fallback to learner profile for unknown roles
      return <LearnerProfile />;
  }
};

export default Profile;