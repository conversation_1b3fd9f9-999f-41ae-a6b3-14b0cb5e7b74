import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Mail, Phone, MapPin, Calendar, Award, Edit, BookOpen, Users, GraduationCap } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const TrainerProfile = () => {
  const { user } = useAuth();

  const userProfile = {
    name: user?.name || "Dr. <PERSON>",
    role: "Senior Medical Trainer",
    department: user?.department || "Medical Education",
    employeeId: "TRN-2024-001",
    email: user?.email || "<EMAIL>",
    phone: "+****************",
    location: "Training Center, Floor 4",
    joinDate: "2019-01-20",
    avatar: user?.avatar || "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    specializations: ["Emergency Medicine", "Cardiac Care", "Trauma Response"],
    certifications: 8,
    studentsTrained: 245
  };

  const trainingAchievements = [
    { name: "Excellence in Medical Education", date: "2024-06-15", type: "Teaching" },
    { name: "Advanced Simulation Training Certification", date: "2024-05-20", type: "Certification" },
    { name: "Curriculum Development Award", date: "2024-04-10", type: "Innovation" },
  ];

  const trainingMetrics = [
    { label: "Students Trained", value: userProfile.studentsTrained, icon: Users },
    { label: "Active Courses", value: "12", icon: BookOpen },
    { label: "Certifications", value: userProfile.certifications, icon: GraduationCap },
    { label: "Training Rating", value: "4.9/5", icon: Award },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Trainer Profile</h1>
          <p className="text-muted-foreground">Manage your profile and training information</p>
        </div>
        <Button variant="outline">
          <Edit className="w-4 h-4 mr-2" />
          Edit Profile
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5 text-primary" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Profile Header */}
              <div className="flex items-center gap-6">
                <Avatar className="w-24 h-24 border-4 border-primary/20">
                  <AvatarImage src={userProfile.avatar} alt={userProfile.name} />
                  <AvatarFallback className="text-lg">
                    {userProfile.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-2">
                  <h2 className="text-2xl font-bold">{userProfile.name}</h2>
                  <p className="text-lg text-muted-foreground">{userProfile.role}</p>
                  <div className="flex gap-2">
                    <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                      {userProfile.department}
                    </Badge>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <GraduationCap className="w-3 h-3 mr-1" />
                      Certified Trainer
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Employee ID</p>
                      <p className="font-medium">{userProfile.employeeId}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <p className="font-medium">{userProfile.email}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <p className="font-medium">{userProfile.phone}</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <MapPin className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Office Location</p>
                      <p className="font-medium">{userProfile.location}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Join Date</p>
                      <p className="font-medium">{new Date(userProfile.joinDate).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Specializations */}
              <div>
                <p className="text-sm text-muted-foreground mb-3">Training Specializations</p>
                <div className="flex flex-wrap gap-2">
                  {userProfile.specializations.map((spec, index) => (
                    <Badge key={index} variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                      {spec}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Training Achievements */}
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5 text-success" />
                Training Achievements
              </CardTitle>
              <CardDescription>Your educational accomplishments and training milestones</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {trainingAchievements.map((achievement, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 bg-muted/30 rounded-lg">
                    <Award className="w-8 h-8 text-success" />
                    <div className="flex-1">
                      <h4 className="font-medium">{achievement.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {achievement.type} • {new Date(achievement.date).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge variant="outline" className="text-success border-success/30">
                      {achievement.type}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Training Summary */}
        <div className="space-y-6">
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="text-lg">Training Overview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {trainingMetrics.map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-3">
                    <metric.icon className="w-5 h-5 text-primary" />
                    <span className="text-sm font-medium">{metric.label}</span>
                  </div>
                  <span className="text-lg font-bold text-primary">{metric.value}</span>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="text-lg">Trainer Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <User className="w-4 h-4 mr-2" />
                Update Personal Info
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <BookOpen className="w-4 h-4 mr-2" />
                Manage Courses
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Users className="w-4 h-4 mr-2" />
                View Students
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="w-4 h-4 mr-2" />
                Schedule Training
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <GraduationCap className="w-4 h-4 mr-2" />
                Certification Management
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TrainerProfile;
